using FluentValidation;

namespace ReviewYourTipster.Application.AccountVerification.VerifyAccountVerificationCode;

public class
    VerifyAccountVerificationCodeCommandValidator : AbstractValidator<VerifyAccountVerificationCodeCommand>
{
    public VerifyAccountVerificationCodeCommandValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty()
            .WithMessage("Code is required")
            .WithErrorCode("Code.Required")
            .Length(6)
            .WithMessage("Code must be 6 digits")
            .WithErrorCode("Code.InvalidLength");
    }
}